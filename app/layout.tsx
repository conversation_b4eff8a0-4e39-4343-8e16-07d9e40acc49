import type React from "react"
import type { Metadata } from "next"
import { JetBrains_Mono } from "next/font/google"
import "./globals.css"
import { AuthProvider } from "@/contexts/auth-context"


// Typography fonts according to design system
const jetbrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  variable: "--font-mono",
  display: "swap",
  weight: ["400"], // For statistics, numbers, and buttons only
})

export const metadata: Metadata = {
  title: "TRADEFORM",
  description: "Advanced AI-powered trading platform with real-time insights and proven strategies.",
  keywords: "trading, AI trading, investment, financial markets",
  authors: [{ name: "Tradeform Team" }],

  // Favicon and app icons
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/favicon.svg', type: 'image/svg+xml' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon-96x96.png', sizes: '96x96', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      { rel: 'mask-icon', url: '/safari-pinned-tab.svg', color: '#4ade80' },
    ],
  },

  // PWA and mobile app configuration
  manifest: '/site.webmanifest',

  // Open Graph
  openGraph: {
    title: "TRADEFORM",
    description: "Advanced AI-powered trading platform with real-time insights and proven strategies.",
    url: "https://tradeform.com",
    siteName: "TRADEFORM",
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'TRADEFORM - Advanced AI Trading Platform',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },

  // Twitter Card
  twitter: {
    card: 'summary_large_image',
    title: "TRADEFORM",
    description: "Advanced AI-powered trading platform with real-time insights and proven strategies.",
    images: ['/og-image.png'],
    creator: '@tradeform',
  },

  // Additional metadata
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },

  // Theme color for mobile browsers
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#000000' },
  ],

  // Viewport configuration
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${jetbrainsMono.variable}`}>
      <head>
        <link href="https://api.fontshare.com/v2/css?f[]=satoshi@400,700,800&display=swap" rel="stylesheet" />
      </head>
      <body className="font-satoshi antialiased">
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  )
}
